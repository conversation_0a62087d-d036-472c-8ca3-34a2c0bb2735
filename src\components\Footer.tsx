import { Heart, Github, Linkedin, Mail } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';

const Footer = () => {
  return (
    <footer className="border-t border-border/10 py-12">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex flex-col md:flex-row items-center justify-between">
          <div className="flex items-center space-x-2 mb-4 md:mb-0">
            <span className="text-muted-foreground">
              © 2025 Harshith
            </span>
          </div>
          
          <div className="flex items-center space-x-4">
            <Button variant="ghost" size="sm" asChild>
              <a
                href="mailto:<EMAIL>"
                target="_blank"
                rel="noopener noreferrer"
                className="hover-scale"
              >
                <Mail className="h-4 w-4" />
              </a>
            </Button>
            <Button variant="ghost" size="sm" asChild>
              <a
                href="https://github.com/Harshith106"
                target="_blank"
                rel="noopener noreferrer"
                className="hover-scale"
              >
                <Github className="h-4 w-4" />
              </a>
            </Button>
            <Button variant="ghost" size="sm" asChild>
              <a
                href="https://www.linkedin.com/in/p-harshith-09b357354/"
                target="_blank"
                rel="noopener noreferrer"
                className="hover-scale"
              >
                <Linkedin className="h-4 w-4" />
              </a>
            </Button>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;