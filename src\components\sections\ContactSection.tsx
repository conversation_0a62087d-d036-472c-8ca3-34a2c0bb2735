import { motion } from 'framer-motion';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Mail,Instagram, Github, Linkedin, Send } from 'lucide-react';
import { useState } from 'react';

const ContactSection = () => {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    subject: '',
    message: ''
  });

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    console.log('Form submitted:', formData);
    // Handle form submission logic here
    setFormData({
      name: '',
      email: '',
      subject: '',
      message: ''
    });
  };

  return (
    <section id="contact" className="py-20">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div 
          initial={{
            opacity: 0,
            y: 20
          }} 
          whileInView={{
            opacity: 1,
            y: 0
          }} 
          transition={{
            duration: 0.6
          }} 
          viewport={{
            once: true
          }} 
          className="mb-16 text-left"
        >
          <h2 className="text-4xl md:text-5xl font-bold mb-10 gradient-text flex items-center">
            <span className="text-primary mr-2">&lt;</span>Contact/<span className="text-primary ml-1">&gt;</span>
          </h2>
          <p className="text-xl text-muted-foreground max-w-3xl">
            Ready to collaborate on exciting projects or discuss opportunities
          </p>
        </motion.div>

        <div className="grid lg:grid-cols-2 gap-12">
          <motion.div 
            initial={{
              opacity: 0,
              x: -50
            }} 
            whileInView={{
              opacity: 1,
              x: 0
            }} 
            transition={{
              duration: 0.8
            }} 
            viewport={{
              once: true
            }} 
            className="space-y-6"
          >
            <Card className="border-border/20">
              <CardContent className="p-8">
                <h3 className="text-2xl font-bold mb-6 text-foreground">Contact Information</h3>
                
                <div className="space-y-4">
                  <div className="flex items-center space-x-4">
                    <div className="w-12 h-12 rounded-sm bg-primary/10 flex items-center justify-center">
                      <Mail className="w-5 h-5 text-primary" />
                    </div>
                    <div>
                      <div className="font-medium text-foreground">Email</div>
                      <div className="text-muted-foreground"><EMAIL></div>
                    </div>
                  </div>
                  <div className="flex items-center space-x-4">
                    <div className="w-12 h-12 rounded-sm bg-primary/10 flex items-center justify-center">
                      <Instagram className="w-5 h-5 text-primary" />
                    </div>
                    <div>
                      <div className="font-medium text-foreground">Instagram</div>
                      <div className="text-muted-foreground">harshith_._106</div>
                    </div>
                  </div>
                </div>
                
                <div className="mt-8 pt-6 border-t border-border/20">
                  <div className="flex space-x-4">
                    <Button variant="outline" size="sm" asChild className="hover-scale">
                      <a href="https://github.com/Harshith106" target="_blank" rel="noopener noreferrer">
                        <Github className="w-4 h-4 mr-2" />
                        GitHub
                      </a>
                    </Button>
                    <Button variant="outline" size="sm" asChild className="hover-scale">
                      <a href="https://www.linkedin.com/in/p-harshith-09b357354/" target="_blank" rel="noopener noreferrer">
                        <Linkedin className="w-4 h-4 mr-2" />
                        LinkedIn
                      </a>
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>

          <motion.div 
            initial={{
              opacity: 0,
              x: 50
            }} 
            whileInView={{
              opacity: 1,
              x: 0
            }} 
            transition={{
              duration: 0.8
            }} 
            viewport={{
              once: true
            }}
          >
            <Card>
              <CardContent className="p-8">
                <h3 className="text-2xl font-bold mb-6 text-foreground">Send a Message</h3>
                
                <form onSubmit={handleSubmit} className="space-y-4">
                  <div>
                    <Input 
                      name="name" 
                      placeholder="Your Name" 
                      value={formData.name} 
                      onChange={handleInputChange} 
                      className="border-border/20 focus:border-primary/50" 
                      required 
                    />
                  </div>
                  
                  <div>
                    <Input 
                      name="email" 
                      type="email" 
                      placeholder="Your Email" 
                      value={formData.email} 
                      onChange={handleInputChange} 
                      className="border-border/20 focus:border-primary/50" 
                      required 
                    />
                  </div>

                  <div>
                    <Input 
                      name="subject" 
                      placeholder="Subject" 
                      value={formData.subject} 
                      onChange={handleInputChange} 
                      className="border-border/20 focus:border-primary/50" 
                      required 
                    />
                  </div>
                  
                  <div>
                    <Textarea 
                      name="message" 
                      placeholder="Your Message" 
                      value={formData.message} 
                      onChange={handleInputChange} 
                      className="border-border/20 focus:border-primary/50 min-h-[120px] resize-none" 
                      required 
                    />
                  </div>
                  
                  <Button type="submit" variant="gradient" size="lg" className="w-full group">
                    Send Message
                    <Send className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform" />
                  </Button>
                </form>
              </CardContent>
            </Card>
          </motion.div>
        </div>
      </div>
    </section>
  );
};

export default ContactSection; 