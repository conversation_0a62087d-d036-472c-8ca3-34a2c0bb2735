import { motion } from 'framer-motion';
import { GraduationCap, Code, Brain, Target } from 'lucide-react';
import Layout from '@/components/Layout';

const About = () => {
  return (
    <Layout>
      <div className="py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Header */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <h1 className="text-4xl md:text-6xl font-bold mb-6">
              About <span className="gradient-text">Me</span>
            </h1>
            <p className="text-xl max-w-3xl mx-auto">
              A passionate B.Tech student on a journey to explore real-world opportunities as a developer.
            </p>
          </motion.div>

          {/* Main Content */}
          <div className="grid lg:grid-cols-2 gap-12 items-center mb-20">
            {/* Story */}
            <motion.div
              initial={{ opacity: 0, x: -50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
              className="space-y-6"
            >
              <h2 className="text-3xl font-bold mb-6">My Story</h2>
              <p className="text-lg text-muted-foreground leading-relaxed">
                I'm a B.Tech student eager to explore real-world opportunities as a developer. 
                My journey in technology started with curiosity about how software can solve 
                everyday problems, and it has evolved into a deep passion for machine learning 
                and algorithmic problem-solving.
              </p>
              <p className="text-lg text-muted-foreground leading-relaxed">
                I'm deeply interested in machine learning and love solving Data Structures & 
                Algorithms problems. Every project I work on is an opportunity to learn something 
                new and push the boundaries of what I can create.
              </p>
            </motion.div>

            {/* Values & Interests */}
            <motion.div
              initial={{ opacity: 0, x: 50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8, delay: 0.4 }}
              className="space-y-6"
            >
              <div className="grid grid-cols-2 gap-6">
                <div className="p-6 rounded-xl hover-scale border border-border/30 bg-card">
                  <Code className="h-8 w-8 text-primary mb-4" />
                  <h3 className="font-semibold mb-2">Development</h3>
                  <p className="text-sm text-muted-foreground">
                    Building web applications with modern technologies
                  </p>
                </div>
                
                <div className="p-6 rounded-xl hover-scale border border-border/30 bg-card">
                  <Brain className="h-8 w-8 text-accent mb-4" />
                  <h3 className="font-semibold mb-2">Machine Learning</h3>
                  <p className="text-sm text-muted-foreground">
                    Creating intelligent solutions with AI
                  </p>
                </div>
                
                <div className="p-6 rounded-xl hover-scale border border-border/30 bg-card">
                  <Target className="h-8 w-8 text-purple-400 mb-4" />
                  <h3 className="font-semibold mb-2">Problem Solving</h3>
                  <p className="text-sm text-muted-foreground">
                    DSA challenges and algorithmic thinking
                  </p>
                </div>
              </div>
            </motion.div>
          </div>

          {/* Education */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.6 }}
            className="p-8 rounded-2xl border border-border/30 bg-card"
          >
            <h2 className="text-3xl font-bold mb-8 text-center">Education</h2>
            <div className="max-w-2xl mx-auto">
              <div className="flex items-start space-x-4">
                <div className="flex-shrink-0">
                  <div className="w-12 h-12 bg-gradient-to-br from-primary to-accent rounded-full flex items-center justify-center">
                    <GraduationCap className="h-6 w-6 text-white" />
                  </div>
                </div>
                <div>
                  <h3 className="text-xl font-semibold mb-2">Bachelor of Technology (B.Tech)</h3>
                  <p className="text-muted-foreground mb-2">Currently Pursuing</p>
                  <p className="text-sm text-muted-foreground">
                    Focused on Computer Science fundamentals, software engineering principles, 
                    and practical application development. Actively participating in coding 
                    competitions and hackathons to enhance problem-solving skills.
                  </p>
                </div>
              </div>
            </div>
          </motion.div>
        </div>
      </div>
    </Layout>
  );
};

export default About;