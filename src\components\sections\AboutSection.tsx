import { motion } from 'framer-motion';
import { Code } from 'lucide-react';
import { FaReact } from "react-icons/fa";
const AboutSection = () => {
  return (
    <section id="about" className="py-20">
      <div className="w-full px-4 sm:px-6 lg:px-12">
        <motion.div 
          initial={{
            opacity: 0,
            y: 20
          }} 
          whileInView={{
            opacity: 1,
            y: 0
          }} 
          transition={{
            duration: 0.6
          }} 
          viewport={{
            once: true
          }} 
          className="mb-16 text-left"
        >
          <h2 className="text-4xl md:text-5xl font-bold mb-10 gradient-text flex items-center">
            <span className="text-primary mr-2">&lt;</span>About Me/<span className="text-primary ml-1">&gt;</span>
          </h2>
        </motion.div>

        <div className="w-full flex flex-col lg:flex-row gap-8 items-stretch">
          <motion.div 
            initial={{
              opacity: 0,
              x: -50
            }} 
            whileInView={{
              opacity: 1,
              x: 0
            }} 
            transition={{
              duration: 0.8
            }} 
            viewport={{
              once: true
            }} 
            className="space-y-6 flex-1 min-w-0 lg:w-1/2"
          >
            {/* Who I Am Card */}
            <div className="p-8 rounded-2xl border-border/30">
              <h3 className="text-2xl font-bold mb-4 text-primary">Who I Am</h3>
              <p className="text-lg text-muted-foreground leading-relaxed mb-6">
                I'm a student eager to explore real-world opportunities as a developer. 
                I'm deeply interested in machine learning and love solving DSA problems. 
                My journey in technology is driven by curiosity and a desire to build solutions 
                that make a difference.
              </p>
              <div className="space-y-4">
                <div className="flex items-center space-x-3">
                  <div className="w-2 h-2 bg-accent rounded-full" />
                  <span className="text-foreground">Passionate about Machine Learning & AI</span>
                </div>
                <div className="flex items-center space-x-3">
                  <div className="w-2 h-2 bg-purple-500 rounded-full" />
                  <span className="text-foreground">Data Structures & Algorithms Enthusiast</span>
                </div>
              </div>
            </div>
            
          </motion.div>

          {/* Technologies/Skills Section */}
          <div className="mt-8 flex-1 min-w-0 lg:mt-0 lg:w-1/2 flex">
            <div className="p-8 rounded-2xl h-full  border-border/30"
            >
              <h4 className="text-3xl font-bold mb-10 text-primary">I'm Familiar With</h4>
              <div className="space-y-6">
                {/* Programming Languages */}
                <div>
                  <div className="flex items-center gap-3 mb-2 ml-2">
                    <span className="w-10 h-10 flex items-center justify-center p-2 bg-primary/10 rounded">
                      <Code className="w-10 h-10 text-primary" />
                    </span>
                    <span className="text-2xl md:text-2xl font-bold text-foreground">Programming Languages</span>
                  </div>
                  <div className="flex flex-wrap gap-3 ml-10 mt-8">
                    <span className="px-6 py-3 rounded bg-primary/10 text-primary text-sm font-medium shadow-sm hover:bg-primary/20 transition-colors">Python</span>
                    <span className="px-6 py-3 rounded bg-primary/10 text-primary text-sm font-medium shadow-sm hover:bg-primary/20 transition-colors">Java</span>
                  </div>
                </div>
                {/* Web Tech */}
                <div>
                  <div className="flex items-center gap-3 mb-2 ml-2">
                    <span className="w-10 h-10 flex items-center justify-center p-2 bg-primary/10 rounded">
                      <FaReact className="w-10 h-10 text-primary" />
                    </span>
                    <span className="text-2xl md:text-2xl font-bold text-foreground">Web Tech</span>
                  </div>
                  <div className="flex flex-wrap gap-3 ml-10 mt-8">
                    <span className="px-6 py-3 rounded bg-primary/10 text-primary text-sm font-medium shadow-sm hover:bg-primary/20 transition-colors">React</span>
                    <span className="px-6 py-3 rounded bg-primary/10 text-primary text-sm font-medium shadow-sm hover:bg-primary/20 transition-colors">Tailwind CSS</span>
                    <span className="px-6 py-3 rounded bg-primary/10 text-primary text-sm font-medium shadow-sm hover:bg-primary/20 transition-colors">Flask</span>
                    <span className="px-6 py-3 rounded bg-primary/10 text-primary text-sm font-medium shadow-sm hover:bg-primary/20 transition-colors">MySQL</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default AboutSection; 