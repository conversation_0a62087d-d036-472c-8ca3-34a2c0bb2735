import { motion } from 'framer-motion';
import { Card, CardContent } from '@/components/ui/card';
import { PinContainer } from '@/components/ui/3d-pin';

const ProjectsSection = () => {
  const projects = [
    {
      title: "Attendance Tracking Website",
      description: "A web app for students to check attendance via a secure login system.",
      tech: ["HTML", "CSS", "JavaScript", "Flask","Selenium"],
      features: ["Student login", "Attendance percentage calculator", "Dashboard UI"],
      image: "https://images.unsplash.com/photo-1513258496099-48168024aec0?auto=format&fit=crop&w=600&q=80", // classroom/attendance
      github: "https://github.com/Harshith106/Attendly",
      live: "https://attendly-039.onrender.com/"
    }, 
    {
      title: "Voxtora AI Tool",
      description: "AI-powered tool to summarize YouTube videos into structured downloadable notes.",
      tech: ["Python", "Flask", "OpenAI API", "YouTube API"],
      features: ["AI-based note generation", "PDF export", "User input via link"],
      image: "https://images.unsplash.com/photo-1461749280684-dccba630e2f6?auto=format&fit=crop&w=600&q=80", // AI/tech
      github: "https://github.com/Harshith106/VoxtoraNotesGenerator",
      live: "https://github.com/Harshith106/VoxtoraNotesGenerator"
    }, 
    {
      title: "Weather Forecast Website",
      description: "A real-time weather app with hourly and weekly forecasts.",
      tech: ["React", "OpenWeatherMap API", "Tailwind CSS"],
      features: ["Location-based data", "Multiple views", "Live updates"],
      image: "https://images.unsplash.com/photo-1506744038136-46273834b3fb?auto=format&fit=crop&w=600&q=80", // weather
      github: "https://github.com/Harshith106/ReactWeather",
      live: "https://github.com/Harshith106/ReactWeather"
    }, 
    {
      title: "Personal Portfolio Website",
      description: "A modern, responsive portfolio website showcasing projects and skills.",
      tech: ["React", "Tailwind CSS", "JavaScript"],
      features: ["Dark theme", "Smooth animations", "Responsive design"],
      image: "https://images.unsplash.com/photo-1519125323398-675f0ddb6308?auto=format&fit=crop&w=600&q=80", // portfolio
      github: "https://github.com/harshith/portfolio",
      live: "https://portfolio-demo.com"
    }
  ];

  return (
    <section id="projects" className="py-20">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div 
          initial={{
            opacity: 0,
            y: 20
          }} 
          whileInView={{
            opacity: 1,
            y: 0
          }} 
          transition={{
            duration: 0.6
          }} 
          viewport={{
            once: true
          }} 
          className="mb-16 text-left"
        >
          <h2 className="text-4xl md:text-5xl font-bold mb-10 gradient-text flex items-center">
            <span className="text-primary mr-2">&lt;</span>Featured Projects/<span className="text-primary ml-1">&gt;</span>
          </h2>
          <p className="text-xl text-muted-foreground max-w-3xl">
            Showcasing practical applications of technology to solve real-world problems
          </p>
        </motion.div>

        <div className="flex flex-col gap-16 w-full">
          {projects.map((project, index) => {
            const isEven = index % 2 === 1;
            return (
            <motion.div 
              key={project.title} 
              initial={{
                opacity: 0,
                y: 30
              }} 
              whileInView={{
                opacity: 1,
                y: 0
              }} 
              transition={{
                delay: index * 0.1,
                duration: 0.6
              }} 
              viewport={{
                once: true
              }} 
              whileHover={{
                scale: 1.02
              }} 
                className="group w-full"
            >
                <div className={`flex flex-col md:flex-row ${isEven ? 'md:flex-row-reverse' : ''} items-stretch w-full gap-12`}>
                  {/* Info Section (wider) */}
                  <div className="flex-[3] w-full max-w-3xl h-[20rem] flex items-stretch">
                    <Card className="h-full transition-all duration-300 overflow-hidden flex-1 flex flex-col justify-center">
                      <CardContent className="p-6 flex-1 flex flex-col justify-center">
                  <h3 className="text-xl font-bold mb-3 text-foreground group-hover:text-primary transition-colors">
                    {project.title}
                  </h3>
                  <p className="text-muted-foreground mb-4 leading-relaxed">
                    {project.description}
                  </p>
                  <div className="mb-4">
                    <div className="flex flex-wrap gap-2 mb-3">
                      {project.tech.map(tech => (
                        <span key={tech} className="px-4 py-2 text-xs rounded bg-primary/10 text-primary border border-primary/20">
                          {tech}
                        </span>
                      ))}
                    </div>
                  </div>
                  <div className="space-y-2">
                    <h4 className="text-sm font-semibold text-foreground">Key Features:</h4>
                    {project.features.map((feature, featureIndex) => (
                      <div key={featureIndex} className="flex items-center space-x-2">
                        <div className="w-1 h-1 rounded-full bg-accent" />
                        <span className="text-sm text-muted-foreground">{feature}</span>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
                  </div>
                  {/* 3D Pin Section (narrower) */}
                  <div className={`flex-[1] w-full max-w-xs lg:max-w-2xl h-40 md:h-[20rem] flex items-center justify-center ${isEven ? 'lg:-translate-x-12' : 'lg:translate-x-12'}`}>
                    <PinContainer
                      title={project.title}
                      href={project.live}
                      className="w-full h-full"
                    >
                      <div className="flex basis-full flex-col items-center justify-center p-4 tracking-tight w-[16rem] h-[16rem] bg-gradient-to-br from-slate-800 via-slate-700 to-slate-900 rounded-lg">
                        <h3 className="text-center font-bold text-lg text-white leading-tight">
                          {project.title}
                        </h3>
                      </div>
                    </PinContainer>
                  </div>
                </div>
            </motion.div>
            );
          })}
        </div>
      </div>
    </section>
  );
};

export default ProjectsSection; 