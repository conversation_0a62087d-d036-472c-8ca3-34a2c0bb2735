import { motion } from 'framer-motion';
import { Code, Database, Cpu, Layers, Globe, Server } from 'lucide-react';
import Layout from '@/components/Layout';

const skillCategories = [
  {
    title: 'Languages',
    icon: Code,
    skills: ['Java', 'Python', 'JavaScript'],
    color: 'from-blue-500 to-blue-600'
  },
  {
    title: 'Frontend',
    icon: Globe,
    skills: ['React', 'Tailwind CSS', 'HTML', 'CSS'],
    color: 'from-purple-500 to-purple-600'
  },
  {
    title: 'Backend',
    icon: Server,
    skills: ['Flask', 'REST APIs'],
    color: 'from-green-500 to-green-600'
  },
  {
    title: 'Database',
    icon: Database,
    skills: ['MySQL'],
    color: 'from-orange-500 to-orange-600'
  },
  {
    title: 'Python Libraries',
    icon: Cpu,
    skills: ['NumPy', 'Pandas', 'Selenium', 'Scikit-learn'],
    color: 'from-red-500 to-red-600'
  },
];

const Skills = () => {
  return (
    <Layout>
      <div className="py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Header */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <h1 className="text-4xl md:text-6xl font-bold mb-6">
              My <span className="gradient-text">Skills</span>
            </h1>
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
              A comprehensive overview of my technical skills and the tools I use to bring ideas to life.
            </p>
          </motion.div>

          {/* Skills Grid */}
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6 mb-12">
            {skillCategories.map((category, index) => (
              <motion.div
                key={category.title}
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                className="p-6 rounded-xl hover-scale group border border-border/30 bg-card"
              >
                <div className="flex items-center mb-4">
                  <div className={`w-12 h-12 rounded-lg bg-gradient-to-r ${category.color} flex items-center justify-center mr-4`}>
                    <category.icon className="h-6 w-6 text-white" />
                  </div>
                  <h3 className="text-xl font-semibold">{category.title}</h3>
                </div>
                
                <div className="space-y-2">
                  {category.skills.map((skill, skillIndex) => (
                    <motion.div
                      key={skill}
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ duration: 0.4, delay: (index * 0.1) + (skillIndex * 0.05) }}
                      className="flex items-center space-x-2"
                    >
                      <div className="w-2 h-2 rounded-full bg-primary/60" />
                      <span className="text-muted-foreground">{skill}</span>
                    </motion.div>
                  ))}
                </div>
              </motion.div>
            ))}
          </div>

          {/* Learning Journey */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.8 }}
            className="p-8 rounded-2xl text-center border border-border/30 bg-card"
          >
            <h2 className="text-3xl font-bold mb-6">Continuous Learning</h2>
            <p className="text-lg text-muted-foreground mb-8 max-w-3xl mx-auto">
              As a passionate developer, I'm always exploring new technologies and expanding my skill set. 
              Currently diving deeper into advanced machine learning concepts and modern web development frameworks.
            </p>
            
            <div className="grid md:grid-cols-3 gap-6">
              <div className="space-y-2">
                <div className="text-2xl font-bold gradient-text">5+</div>
                <div className="text-sm text-muted-foreground">Programming Languages</div>
              </div>
              <div className="space-y-2">
                <div className="text-2xl font-bold gradient-text">10+</div>
                <div className="text-sm text-muted-foreground">Technologies & Tools</div>
              </div>
              <div className="space-y-2">
                <div className="text-2xl font-bold gradient-text">3+</div>
                <div className="text-sm text-muted-foreground">Major Projects</div>
              </div>
            </div>
          </motion.div>
        </div>
      </div>
    </Layout>
  );
};

export default Skills;