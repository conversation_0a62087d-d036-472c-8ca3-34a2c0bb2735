import { motion } from 'framer-motion';
import { ArrowRight, Mail } from 'lucide-react';
import { PiReadCvLogoFill } from "react-icons/pi";
import { Button } from '@/components/ui/button';
import heroBackground from '@/assets/hero-background.jpg';

const HeroSection = () => {
  return (
    <section id="home" className="min-h-screen relative overflow-hidden">
      {/* Hero Background */}
      <div 
        className="absolute inset-0 bg-cover bg-center bg-no-repeat opacity-20" 
        style={{
          backgroundImage: `url(${heroBackground})`
        }} 
      />
      
      {/* Main Content */}
      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
        <div className="flex flex-col items-start justify-center min-h-[80vh] text-left space-y-8">
          
          <motion.div 
            initial={{
              opacity: 0,
              y: 30
            }} 
            animate={{
              opacity: 1,
              y: 0
            }} 
            transition={{
              duration: 0.8,
              ease: "easeOut"
            }} 
            className="space-y-6 text-left mb-8"
          >
            <motion.h1 
              initial={{
                opacity: 0,
                y: 30
              }} 
              animate={{
                opacity: 1,
                y: 0
              }} 
              transition={{
                delay: 0.2,
                duration: 0.8
              }} 
              className="text-6xl md:text-7xl lg:text-8xl font-bold leading-tight"
            >
              Hi, I'm <span className="text-outlined">Harshith</span>
            </motion.h1>
            
            <motion.p 
              initial={{
                opacity: 0,
                y: 20
              }} 
              animate={{
                opacity: 1,
                y: 0
              }} 
              transition={{
                delay: 0.4,
                duration: 0.6
              }} 
              className="text-xl md:text-xl lg:text-2xl text-muted-foreground max-w-4xl leading-relaxed"
            >
              A tech enthusiast and <span className="text-foreground font-semibold">B.Tech student</span> with a passion for building intelligent solutions using{' '}
              <span className="gradient-text font-semibold">Machine Learning</span>,{' '}
              <span className="gradient-text font-semibold">Java</span>, and{' '}
              <span className="gradient-text font-semibold">Python</span>
            </motion.p>
          </motion.div>

          <motion.div 
            initial={{
              opacity: 0,
              y: 20
            }} 
            animate={{
              opacity: 1,
              y: 0
            }} 
            transition={{
              delay: 0.6,
              duration: 0.6
            }} 
            className="flex flex-col sm:flex-row gap-4 mb-4"
          >
            <Button asChild variant="gradient" size="lg" className="group">
              <a href="#projects">
                View Projects
                <ArrowRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform" />
              </a>
            </Button>
            
            <Button asChild variant="hero" size="lg">
              <a href="/Harshith.pdf" target="_blank" rel="noopener noreferrer">
                Resume
                <PiReadCvLogoFill className="ml-2 h-4 w-4" />
              </a>
            </Button>
          </motion.div>
        </div>

        
        <motion.div 
          initial={{
            opacity: 0
          }} 
          animate={{
            opacity: 1
          }} 
          transition={{
            delay: 1,
            duration: 0.6
          }} 
          className="absolute bottom-8 left-1/2 transform -translate-x-1/2"
        >
        </motion.div>
      </div>
    </section>
  );
};

export default HeroSection; 