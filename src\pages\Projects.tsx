import { motion } from 'framer-motion';
import { ExternalLink, Github, Users, Calendar, Bot, Cloud } from 'lucide-react';
import { Button } from '@/components/ui/button';
import Layout from '@/components/Layout';

const projects = [
  {
    title: 'Attendance Tracking Website',
    description: 'A web application for students to check attendance via a secure login system.',
    longDescription: 'Built a comprehensive attendance management system that allows students to track their attendance percentages across different subjects. Features include secure authentication, real-time data updates, and an intuitive dashboard.',
    tech: ['HTML', 'CSS', 'JavaScript', 'Flask', 'SQLite'],
    features: ['Student login system', 'Attendance percentage calculator', 'Interactive dashboard UI', 'Real-time data visualization'],
    icon: Users,
    color: 'from-blue-500 to-blue-600',
    github: 'https://github.com/harshith/attendance-tracker',
    live: 'https://attendance-tracker-demo.com',
    image: 'https://images.unsplash.com/photo-1513258496099-48168024aec0?auto=format&fit=crop&w=800&q=80',
    status: 'Completed'
  },
  {
    title: 'Voxtora AI Tool',
    description: 'AI-powered tool to summarize YouTube videos into structured downloadable notes.',
    longDescription: 'Developed an intelligent system that processes YouTube video content using AI to generate comprehensive, structured notes. Users can input video links and receive well-organized summaries in PDF format.',
    tech: ['Python', 'Flask', 'OpenAI API', 'YouTube API', 'PDF Generation'],
    features: ['AI-based note generation', 'PDF export functionality', 'User input via video links', 'Structured content organization'],
    icon: Bot,
    color: 'from-purple-500 to-purple-600',
    github: 'https://github.com/harshith/voxtora-ai',
    live: 'https://voxtora-ai-demo.com',
    image: 'https://images.unsplash.com/photo-1461749280684-dccba630e2f6?auto=format&fit=crop&w=800&q=80',
    status: 'In Progress'
  },
  {
    title: 'Weather Forecast Website',
    description: 'A real-time weather application with hourly and weekly forecasts.',
    longDescription: 'Created a responsive weather application that provides accurate, real-time weather information. Features location-based data, multiple forecast views, and a clean, intuitive user interface.',
    tech: ['React', 'OpenWeatherMap API', 'CSS', 'JavaScript'],
    features: ['Location-based weather data', 'Hourly and weekly forecasts', 'Live weather updates', 'Responsive design'],
    icon: Cloud,
    color: 'from-cyan-500 to-cyan-600',
    github: 'https://github.com/harshith/weather-app',
    live: 'https://weather-app-demo.com',
    image: 'https://images.unsplash.com/photo-1506744038136-46273834b3fb?auto=format&fit=crop&w=800&q=80',
    status: 'Completed'
  },
  {
    title: 'Personal Portfolio Website',
    description: 'This modern, responsive portfolio showcasing my projects and skills.',
    longDescription: 'Designed and developed a comprehensive portfolio website using modern web technologies. Features include dark mode design, smooth animations, and a responsive layout optimized for all devices.',
    tech: ['React', 'Tailwind CSS', 'Framer Motion', 'TypeScript'],
    features: ['Dark mode design', 'Smooth animations', 'Responsive layout', 'Modern UI/UX'],
    icon: Calendar,
    color: 'from-green-500 to-green-600',
    github: 'https://github.com/harshith/portfolio',
    live: 'https://harshith-portfolio.com',
    image: 'https://images.unsplash.com/photo-1519125323398-675f0ddb6308?auto=format&fit=crop&w=800&q=80',
    status: 'Completed'
  }
];

const Projects = () => {
  return (
    <Layout>
      <div className="py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Header */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <h1 className="text-4xl md:text-6xl font-bold mb-6">
              My <span className="gradient-text">Projects</span>
            </h1>
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
              A collection of projects that showcase my skills in web development, machine learning, and problem-solving.
            </p>
          </motion.div>

          {/* Projects Grid */}
          <div className="space-y-12">
            {projects.map((project, index) => (
              <motion.div
                key={project.title}
                initial={{ opacity: 0, y: 50 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: index * 0.2 }}
                className={`p-8 rounded-2xl border border-border/30 bg-card ${index % 2 === 0 ? 'lg:flex-row' : 'lg:flex-row-reverse'} flex flex-col lg:flex gap-8 hover-scale`}
              >
                {/* Project Image */}
                <div className="lg:w-1/2">
                  <div className="relative overflow-hidden rounded-xl bg-secondary/20 h-64 lg:h-80 flex items-center justify-center group cursor-pointer transition-transform duration-300 hover:scale-105">
                    <img
                      src={project.image}
                      alt={project.title}
                      className="absolute inset-0 w-full h-full object-cover opacity-60"
                    />
                    <project.icon className="h-24 w-24 text-muted-foreground/30 relative z-10" />
                    <div className="absolute inset-0 bg-gradient-to-t from-background/80 to-transparent" />
                    <div className={`absolute top-4 right-4 px-3 py-1 rounded-full text-xs font-medium bg-gradient-to-r ${project.color} text-white`}>
                      {project.status}
                    </div>
                    {/* Hover overlay with links */}
                    <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center space-x-4">
                      <Button variant="gradient" size="sm" asChild>
                        <a href={project.live} target="_blank" rel="noopener noreferrer">
                          <ExternalLink className="h-4 w-4 mr-2" />
                          Live Demo
                        </a>
                      </Button>
                      <Button variant="glass" size="sm" asChild>
                        <a href={project.github} target="_blank" rel="noopener noreferrer">
                          <Github className="h-4 w-4 mr-2" />
                          Source Code
                        </a>
                      </Button>
                    </div>
                  </div>
                </div>

                {/* Project Content */}
                <div className="lg:w-1/2 space-y-6">
                  <div>
                    <h3 className="text-2xl font-bold mb-3">{project.title}</h3>
                    <p className="text-muted-foreground mb-4">{project.longDescription}</p>
                  </div>

                  {/* Tech Stack */}
                  <div>
                    <h4 className="font-semibold mb-2">Technologies Used:</h4>
                    <div className="flex flex-wrap gap-2">
                      {project.tech.map((tech) => (
                        <span
                          key={tech}
                          className="px-3 py-1 bg-secondary/30 text-sm rounded-full border border-border/50"
                        >
                          {tech}
                        </span>
                      ))}
                    </div>
                  </div>

                  {/* Features */}
                  <div>
                    <h4 className="font-semibold mb-2">Key Features:</h4>
                    <ul className="space-y-1">
                      {project.features.map((feature) => (
                        <li key={feature} className="flex items-center space-x-2 text-sm text-muted-foreground">
                          <div className="w-1.5 h-1.5 rounded-full bg-primary" />
                          <span>{feature}</span>
                        </li>
                      ))}
                    </ul>
                  </div>

                  {/* Action Buttons */}
                  <div className="flex space-x-4 pt-4">
                    <Button variant="gradient" size="sm" asChild>
                      <a href={project.live} target="_blank" rel="noopener noreferrer">
                        <ExternalLink className="h-4 w-4 mr-2" />
                        Live Demo
                      </a>
                    </Button>
                    <Button variant="glass" size="sm" asChild>
                      <a href={project.github} target="_blank" rel="noopener noreferrer">
                        <Github className="h-4 w-4 mr-2" />
                        Source Code
                      </a>
                    </Button>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>

          {/* Call to Action */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 1 }}
            className="text-center mt-16"
          >
            <div className="p-8 rounded-2xl border border-border/30 bg-card">
              <h2 className="text-3xl font-bold mb-4">Interested in collaborating?</h2>
              <p className="text-muted-foreground mb-6">
                I'm always looking for new opportunities to learn and grow. Let's build something amazing together!
              </p>
              <Button variant="gradient" size="lg" asChild>
                <a href="/contact">Get In Touch</a>
              </Button>
            </div>
          </motion.div>
        </div>
      </div>
    </Layout>
  );
};

export default Projects;