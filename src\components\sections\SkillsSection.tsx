import { motion } from 'framer-motion';
import { Accordion, AccordionItem, AccordionTrigger, AccordionContent } from '@/components/ui/accordion';
import { Card, CardContent } from '@/components/ui/card';
import { Code, Database, Palette, Cpu } from 'lucide-react';

const SkillsSection = () => {
  const skills = [
    {
      category: "Programming Languages",
      items: ["Java", "Python", "HTML", "CSS", "JavaScript"],
      icon: Code,
      color: "from-blue-500 to-purple-500"
    }, 
    {
      category: "Frontend",
      items: ["React", "Tailwind CSS", "Framer Motion"],
      icon: Palette,
      color: "from-green-500 to-teal-500"
    }, 
    {
      category: "Backend & Database",
      items: ["Flask", "MySQL", "REST APIs"],
      icon: Database,
      color: "from-orange-500 to-red-500"
    }, 
    {
      category: "Python Libraries",
      items: ["NumPy", "Pandas", "Selenium", "Scikit-learn"],
      icon: Cpu,
      color: "from-purple-500 to-pink-500"
    }
  ];

  return (
    <section id="skills" className="py-20">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div 
          initial={{
            opacity: 0,
            y: 20
          }} 
          whileInView={{
            opacity: 1,
            y: 0
          }} 
          transition={{
            duration: 0.6
          }} 
          viewport={{
            once: true
          }} 
          className="mb-16 text-left"
        >
          <h2 className="text-4xl md:text-5xl font-bold mb-10 gradient-text flex items-center">
            <span className="text-primary mr-2">&lt;</span>Skills & Technologies/<span className="text-primary ml-1">&gt;</span>
          </h2>
          <p className="text-xl text-muted-foreground max-w-3xl text-left">
            A comprehensive toolkit for building modern, intelligent applications
          </p>
        </motion.div>

        <div className="space-y-10">
          {skills.map((skillCategory) => {
            const IconComponent = skillCategory.icon;
            return (
              <div key={skillCategory.category} className="ml-16">
                <div className="flex items-center gap-3 mb-4">
                  <span className={`w-10 h-10 flex items-center justify-center p-2`}>
                    <IconComponent className="w-full h-full text-primary" />
                  </span>
                  <span className="text-2xl md:text-3xl font-bold text-foreground">{skillCategory.category}</span>
                </div>
                <div className="flex flex-wrap gap-3 ml-10 mt-10">
                  {skillCategory.items.map((skill) => (
                    <span
                      key={skill}
                      className="px-6 py-4 rounded bg-primary/10 text-primary text-sm font-medium shadow-sm hover:bg-primary/20 transition-colors"
                    >
                      {skill}
                    </span>
                  ))}
                </div>
              </div>
            );
          })}
        </div>
      </div>
    </section>
  );
};

export default SkillsSection; 