
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 220 40% 5%; 
    --foreground: 210 20% 95%;

    --card: 215 30% 15%;
    --card-foreground: 210 20% 92%;

    --popover: 220 20% 10%;
    --popover-foreground: 210 20% 95%;

    --primary: 190 90% 60%; 
    --primary-foreground: 220 40% 5%;

    --secondary: 215 30% 20%;
    --secondary-foreground: 210 15% 88%;

    --muted: 215 30% 15%;
    --muted-foreground: 210 15% 70%;

    --accent: 190 85% 65%; /* Vibrant Teal/<PERSON>an */
    --accent-foreground: 220 40% 5%;

    --destructive: 0 84% 60%;
    --destructive-foreground: 210 40% 98%;

    --border: 215 25% 25%; 
    --input: 215 30% 15%;
    --ring: 190 90% 60%;

    --radius: 1rem;

    /* Gradients */
    --gradient-hero: linear-gradient(165deg, hsl(206 70% 50% / 0.5), hsl(220 40% 5% / 1));
    --gradient-card: linear-gradient(145deg, hsl(215 30% 15% / 0.7), hsl(215 30% 15% / 0.3));
    --gradient-primary: linear-gradient(135deg, hsl(var(--primary)), hsl(var(--accent)));
    --gradient-blue-main: linear-gradient(90deg, hsl(190, 90%, 60%), hsl(220, 80%, 60%));

    /* Shadows */
    --shadow-glow: 0 0 20px hsl(var(--primary) / 0.18);
    --shadow-card: 0 10px 30px -10px hsl(var(--background) / 0.8);
    --shadow-elegant: 0 20px 60px -20px hsl(var(--primary) / 0.5);

    /* Animations */
    --transition-smooth: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-bounce: all 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55);

    /* Sidebar (Light mode defaults) */
    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    /* New Theme: Deep Blue & Teal -- Based on reference image */
    --background: 220 40% 5%; /* Deeper, more saturated blue-black */
    --foreground: 210 20% 95%;

    --card: 215 30% 15%;
    --card-foreground: 210 20% 92%;

    --popover: 220 20% 10%;
    --popover-foreground: 210 20% 95%;

    --primary: 190 90% 60%; /* Vibrant Teal/Cyan */
    --primary-foreground: 220 40% 5%;

    --secondary: 215 30% 20%;
    --secondary-foreground: 210 15% 88%;

    --muted: 215 30% 15%;
    --muted-foreground: 210 15% 70%;

    --accent: 190 85% 65%; /* Vibrant Teal/Cyan */
    --accent-foreground: 220 40% 5%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 215 25% 25%; /* Subtle blue-tinted border */
    --input: 215 30% 15%;
    --ring: 190 90% 60%; /* Ring color to match primary */
    --gradient-blue-main: linear-gradient(90deg, hsl(190, 90%, 60%), hsl(220, 80%, 60%));

    /* Sidebar (Dark mode) */
    --sidebar-background: 220 40% 7%;
    --sidebar-foreground: 210 20% 95%;
    --sidebar-primary: 190 90% 60%;
    --sidebar-primary-foreground: 220 40% 5%;
    --sidebar-accent: 215 30% 20%;
    --sidebar-accent-foreground: 210 20% 95%;
    --sidebar-border: 215 25% 25%;
    --sidebar-ring: 190 90% 60%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}

@layer components {
  /* Glass Card Effect - Remove glassy styles */
  .glass-card {
    @apply border border-border/30 bg-card;
    /* Removed: backdrop-blur-md, background, box-shadow */
  }
  
  /* Hero Gradient Background - Blue Theme */
  .hero-gradient {
    background: var(--gradient-hero);
  }
  
  /* Glow Effect - Blue Tint */
  .glow {
    box-shadow: var(--shadow-glow);
  }
  
  /* Elegant Shadow - Blue Tint */
  .shadow-elegant {
    box-shadow: var(--shadow-elegant);
  }
  
  /* Smooth Transitions */
  .transition-smooth {
    transition: var(--transition-smooth);
  }
  
  /* Bounce Transitions */
  .transition-bounce {
    transition: var(--transition-bounce);
  }
  
  /* Gradient Text - Blue Theme */
  .gradient-text {
    background: var(--gradient-blue-main);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    line-height: 1.3;
    padding-bottom: 0.18em;
  }
  
  /* Outlined/Hollow Text Effect */
  .text-outlined {
    color: transparent;
    -webkit-text-stroke: 2px hsl(var(--primary));
    text-stroke: 2px hsl(var(--primary));
    filter: drop-shadow(0 0 10px hsl(var(--primary) / 0.3));
  }
  
  /* Hover Scale Animation */
  .hover-scale {
    @apply transition-smooth hover:scale-105;
  }
  
  /* Fade In Animation */
  .fade-in {
    @apply animate-fade-in;
  }
}

@layer utilities {
  /* Custom animations */
  @keyframes fade-in {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
  
  @keyframes slide-in-left {
    from {
      opacity: 0;
      transform: translateX(-50px);
    }
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }
  
  @keyframes slide-in-right {
    from {
      opacity: 0;
      transform: translateX(50px);
    }
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }
  
  @keyframes float {
    0%, 100% {
      transform: translateY(0px);
    }
    50% {
      transform: translateY(-10px);
    }
  }
  
  @keyframes glow-pulse {
    0%, 100% {
      box-shadow: 0 0 20px hsl(var(--primary) / 0.4);
    }
    50% {
      box-shadow: 0 0 40px hsl(var(--primary) / 0.7);
    }
  }
  
  .animate-float {
    animation: float 3s ease-in-out infinite;
  }
  
  .animate-glow-pulse {
    animation: glow-pulse 2s ease-in-out infinite;
  }
  
  .animate-slide-in-left {
    animation: slide-in-left 0.6s ease-out;
  }
  
  .animate-slide-in-right {
    animation: slide-in-right 0.6s ease-out;
  }
}

/* Custom Scrollbar Styles */
::-webkit-scrollbar {
  width: 10px;
  height: 10px;
}
::-webkit-scrollbar-thumb {
  background: hsl(var(--primary));
  border-radius: 8px;
}
::-webkit-scrollbar-track {
  background: hsl(var(--background));
}

/* For Firefox */
html {
  scrollbar-width: thin;
  scrollbar-color: hsl(var(--primary)) hsl(var(--background));
}
