import { motion } from 'framer-motion';
import { Timeline } from '@/components/ui/timeline';
import { GraduationCap, BookOpen } from 'lucide-react';

const EducationSection = () => {
  const educationData = [
    {
      title: "2021 - Present",
      content: (
        <div>
          <div className="mb-4">
            <h4 className="text-lg font-semibold text-foreground mb-2">Bachelor of Technology (B.Tech)</h4>
            <p className="text-muted-foreground mb-4">Computer Science Engineering</p>
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <GraduationCap className="w-4 h-4 text-primary" />
                <span className="text-sm text-muted-foreground">Currently pursuing final year</span>
              </div>
              <div className="flex items-center gap-2">
                <BookOpen className="w-4 h-4 text-accent" />
                <span className="text-sm text-muted-foreground">Focus: Machine Learning, Data Structures & Algorithms</span>
              </div>
            </div>
          </div>
        </div>
      ),
    },
    {
      title: "2019 - 2021",
      content: (
        <div>
          <div className="mb-4">
            <h4 className="text-lg font-semibold text-foreground mb-2">Higher Secondary Education</h4>
            <p className="text-muted-foreground mb-4">Science Stream (PCM - Physics, Chemistry, Mathematics)</p>
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <BookOpen className="w-4 h-4 text-primary" />
                <span className="text-sm text-muted-foreground">Strong foundation in Mathematics and Logic</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-accent rounded-full" />
                <span className="text-sm text-muted-foreground">Developed analytical thinking skills</span>
              </div>
            </div>
          </div>
        </div>
      ),
    },
    {
      title: "2009 - 2019",
      content: (
        <div>
          <div className="mb-4">
            <h4 className="text-lg font-semibold text-foreground mb-2">Primary & Secondary Education</h4>
            <p className="text-muted-foreground mb-4">Foundational learning years</p>
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <BookOpen className="w-4 h-4 text-primary" />
                <span className="text-sm text-muted-foreground">Built strong academic foundation</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-purple-500 rounded-full" />
                <span className="text-sm text-muted-foreground">Early interest in technology and problem-solving</span>
              </div>
            </div>
          </div>
        </div>
      ),
    },
  ];

  return (
    <section id="education" className="pt-10 pb-10 md:py-20">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div 
          initial={{ opacity: 0, y: 20 }} 
          whileInView={{ opacity: 1, y: 0 }} 
          transition={{ duration: 0.6 }} 
          viewport={{ once: true }} 
          className="mb-6 md:mb-16 text-left"
        >
          <h2 className="text-4xl md:text-5xl font-bold mb-10 gradient-text flex items-center">
            <span className="text-primary mr-2">&lt;</span>Educational Journey/<span className="text-primary ml-1">&gt;</span>
          </h2>
          <p className="text-xl text-muted-foreground max-w-3xl">
            My academic path from primary education to current undergraduate studies.
          </p>
        </motion.div>

        <div className="-mt-6 md:mt-0">
          <Timeline data={educationData} />
        </div>
      </div>
    </section>
  );
};

export default EducationSection; 